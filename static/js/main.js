// 🚀 现代化移动应用级JavaScript功能系统

// ===== 🎯 核心功能初始化 =====
document.addEventListener('DOMContentLoaded', function() {
    initializeUI();
    initializeModals();
    initializeNotifications();
    initializeScrollProgress();
    initializeAnimations();
    initializePerformanceOptimizations();
    initializeAccessibility();

    // 页面加载完成
    document.body.classList.add('page-loaded');

    // 自动隐藏通知消息
    setTimeout(hideNotifications, 5000);
});

// ===== 🎨 UI组件初始化 =====
function initializeUI() {
    initializeButtons();
    initializeCards();
    initializeForms();
    initializeSearch();
    initializeTheme();
}

// 🔘 按钮增强
function initializeButtons() {
    document.querySelectorAll('.btn').forEach(button => {
        // 添加波纹效果
        button.addEventListener('click', function(e) {
            createRippleEffect(e, this);
        });

        // 添加加载状态支持
        if (button.type === 'submit') {
            button.addEventListener('click', function() {
                if (this.form && this.form.checkValidity()) {
                    showButtonLoading(this);
                }
            });
        }
    });
}

// 🌊 波纹效果
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple-effect 0.6s ease-out;
        pointer-events: none;
    `;

    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);

    setTimeout(() => ripple.remove(), 600);
}

// ⏳ 按钮加载状态
function showButtonLoading(button) {
    const originalText = button.innerHTML;
    const loadingText = button.dataset.loadingText || '处理中...';

    button.innerHTML = `
        <div class="loading-spinner mr-2"></div>
        ${loadingText}
    `;
    button.disabled = true;
    button.dataset.originalText = originalText;
}

function hideButtonLoading(button) {
    if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
        button.disabled = false;
        delete button.dataset.originalText;
    }
}

// 🃏 卡片增强
function initializeCards() {
    document.querySelectorAll('.card-hover').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 项目卡片特殊效果
    document.querySelectorAll('.project-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.project-icon');
            if (icon) {
                icon.style.transform = 'scale(1.05) rotate(2deg)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.project-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });
    });
}

// 📝 表单增强
function initializeForms() {
    document.querySelectorAll('.form-input').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });

        input.addEventListener('input', function() {
            validateField(this);
        });
    });
}

// ✅ 字段验证
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    let isValid = true;
    let errorMessage = '';

    if (field.required && !value) {
        isValid = false;
        errorMessage = '此字段为必填项';
    } else if (type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        errorMessage = '请输入有效的邮箱地址';
    } else if (type === 'url' && value && !isValidUrl(value)) {
        isValid = false;
        errorMessage = '请输入有效的网址';
    }

    showFieldValidation(field, isValid, errorMessage);
    return isValid;
}

function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function isValidUrl(url) {
    return /^https?:\/\/.+/.test(url);
}

function showFieldValidation(field, isValid, message) {
    const existingError = field.parentElement.querySelector('.form-error');
    if (existingError) {
        existingError.remove();
    }

    field.classList.remove('border-red-500', 'border-green-500');

    if (!isValid && message) {
        field.classList.add('border-red-500');
        const errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        errorElement.textContent = message;
        field.parentElement.appendChild(errorElement);
    } else if (field.value.trim()) {
        field.classList.add('border-green-500');
    }
}

// 🔍 搜索功能增强
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performLiveSearch(query);
                }, 300);
            }
        });
    }
}

function performLiveSearch(query) {
    // 实时搜索建议功能
    console.log('Searching for:', query);
}

// 🎨 主题切换
function initializeTheme() {
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

function toggleTheme() {
    const html = document.documentElement;
    const isDark = html.classList.contains('dark');

    if (isDark) {
        html.classList.remove('dark');
        localStorage.setItem('theme', 'light');
    } else {
        html.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }

    updateThemeIcons();
}

function updateThemeIcons() {
    const darkIcon = document.getElementById('theme-toggle-dark-icon');
    const lightIcon = document.getElementById('theme-toggle-light-icon');

    if (darkIcon && lightIcon) {
        darkIcon.classList.toggle('hidden');
        lightIcon.classList.toggle('hidden');
    }
}

// ===== 🪟 模态框系统 =====
function initializeModals() {
    // 模态框触发器
    document.querySelectorAll('[data-modal-target]').forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal-target');
            showModal(modalId);
        });
    });

    // 模态框关闭按钮
    document.querySelectorAll('[data-modal-close]').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal-container');
            if (modal) hideModal(modal.id);
        });
    });

    // 背景点击关闭
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-backdrop')) {
            const modal = e.target.closest('.modal-container');
            if (modal) hideModal(modal.id);
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal-container:not(.hidden)');
            if (openModal) hideModal(openModal.id);
        }
    });
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    modal.classList.remove('hidden');
    document.body.classList.add('overflow-hidden');

    // 动画效果
    requestAnimationFrame(() => {
        const backdrop = modal.querySelector('.modal-backdrop');
        const panel = modal.querySelector('.modal-panel');

        if (backdrop) backdrop.classList.add('opacity-75');
        if (panel) {
            panel.classList.add('opacity-100', 'translate-y-0');
            panel.classList.remove('opacity-0', 'translate-y-4');
        }
    });

    // 焦点管理
    const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) firstFocusable.focus();
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    const backdrop = modal.querySelector('.modal-backdrop');
    const panel = modal.querySelector('.modal-panel');

    if (backdrop) backdrop.classList.remove('opacity-75');
    if (panel) {
        panel.classList.remove('opacity-100', 'translate-y-0');
        panel.classList.add('opacity-0', 'translate-y-4');
    }

    setTimeout(() => {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }, 300);
}

// ===== 🔔 通知系统 =====
function initializeNotifications() {
    // 自动隐藏现有通知
    document.querySelectorAll('.notification').forEach(notification => {
        setTimeout(() => {
            hideNotification(notification);
        }, 5000);
    });
}

function showNotification(message, type = 'info', duration = 5000) {
    const notification = createNotificationElement(message, type);

    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-20 right-4 z-50 space-y-2';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // 入场动画
    requestAnimationFrame(() => {
        notification.classList.add('animate-slide-down');
    });

    // 自动隐藏
    if (duration > 0) {
        setTimeout(() => hideNotification(notification), duration);
    }

    return notification;
}

function createNotificationElement(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const iconMap = {
        success: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        error: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        warning: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        info: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    };

    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${iconMap[type] || iconMap.info}
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-white hover:text-gray-200 focus:outline-none" onclick="hideNotification(this.closest('.notification'))">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    return notification;
}

function hideNotification(notification) {
    if (!notification) return;

    notification.style.opacity = '0';
    notification.style.transform = 'translateY(-20px)';

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

function hideNotifications() {
    document.querySelectorAll('.notification').forEach(hideNotification);
}

// ===== 📊 滚动进度条 =====
function initializeScrollProgress() {
    const progressBar = document.querySelector('.scroll-progress');
    if (!progressBar) {
        const bar = document.createElement('div');
        bar.className = 'scroll-progress';
        document.body.appendChild(bar);
    }

    function updateScrollProgress() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        const bar = document.querySelector('.scroll-progress');
        if (bar) {
            bar.style.width = Math.min(scrollPercent, 100) + '%';
        }
    }

    window.addEventListener('scroll', throttle(updateScrollProgress, 16));
    updateScrollProgress();
}

// ===== 🎭 动画系统 =====
function initializeAnimations() {
    initializeScrollAnimations();
    initializeCounterAnimations();
    initializeParallaxEffects();
}

function initializeScrollAnimations() {
    const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-slide-up, .animate-slide-down, .animate-scale-in, .animate-bounce-in');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = '0s';
                entry.target.classList.add('animate-visible');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
        observer.observe(element);
    });
}

function initializeCounterAnimations() {
    const counters = document.querySelectorAll('.stats-value');

    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, '')) || 0;
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 16);
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => observer.observe(counter));
}

function initializeParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax-bg, .parallax-element');

    function updateParallax() {
        const scrolled = window.pageYOffset;

        parallaxElements.forEach(element => {
            const rate = element.classList.contains('parallax-bg') ? -0.5 : -0.3;
            const yPos = scrolled * rate;
            element.style.transform = `translateY(${yPos}px)`;
        });
    }

    if (parallaxElements.length > 0) {
        window.addEventListener('scroll', throttle(updateParallax, 16));
    }
}

// ===== ⚡ 性能优化 =====
function initializePerformanceOptimizations() {
    // 图片懒加载
    initializeLazyLoading();

    // 预加载关键资源
    preloadCriticalResources();

    // 优化滚动性能
    optimizeScrollPerformance();
}

function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('loading');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => {
        img.classList.add('loading');
        imageObserver.observe(img);
    });
}

function preloadCriticalResources() {
    const criticalImages = [
        '/static/images/hero-bg.jpg',
        '/static/images/logo.png'
    ];

    criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });
}

function optimizeScrollPerformance() {
    let ticking = false;

    function updateOnScroll() {
        // 批量处理滚动相关的DOM操作
        requestAnimationFrame(() => {
            ticking = false;
        });
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateOnScroll);
            ticking = true;
        }
    });
}

// ===== 🔧 工具函数 =====
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// ===== ♿ 无障碍功能 =====
function initializeAccessibility() {
    // 键盘导航
    document.addEventListener('keydown', function(e) {
        // Tab键焦点管理
        if (e.key === 'Tab') {
            handleTabNavigation(e);
        }

        // Enter键激活按钮
        if (e.key === 'Enter' && e.target.classList.contains('btn')) {
            e.target.click();
        }
    });

    // 焦点指示器
    document.querySelectorAll('button, a, input, select, textarea').forEach(element => {
        element.addEventListener('focus', function() {
            this.classList.add('focus-visible');
        });

        element.addEventListener('blur', function() {
            this.classList.remove('focus-visible');
        });
    });

    // ARIA标签增强
    enhanceAriaLabels();
}

function handleTabNavigation(e) {
    const focusableElements = document.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
    } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
    }
}

function enhanceAriaLabels() {
    // 为按钮添加ARIA标签
    document.querySelectorAll('.btn').forEach(button => {
        if (!button.getAttribute('aria-label') && !button.textContent.trim()) {
            button.setAttribute('aria-label', '按钮');
        }
    });

    // 为表单字段添加ARIA描述
    document.querySelectorAll('.form-input').forEach(input => {
        const label = input.previousElementSibling;
        if (label && label.classList.contains('form-label')) {
            input.setAttribute('aria-labelledby', label.id || 'label-' + Math.random().toString(36).substr(2, 9));
        }
    });
}

// ===== 🎯 高级交互功能 =====
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('已复制到剪贴板', 'success');
        }).catch(() => {
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showNotification('已复制到剪贴板', 'success');
    } catch (err) {
        showNotification('复制失败', 'error');
    }

    document.body.removeChild(textArea);
}

function smoothScrollTo(target, duration = 800) {
    const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
    if (!targetElement) return;

    const targetPosition = targetElement.offsetTop - 100;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = easeInOutQuad(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }

    function easeInOutQuad(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }

    requestAnimationFrame(animation);
}

// ===== 🔄 AJAX辅助函数 =====
function makeRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    };

    const config = { ...defaultOptions, ...options };

    // 添加CSRF令牌
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        config.headers['X-CSRFToken'] = csrfToken.getAttribute('content');
    }

    return fetch(url, config)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('Request failed:', error);
            showNotification('请求失败，请稍后重试', 'error');
            throw error;
        });
}

// ===== 🎨 UI状态管理 =====
function showLoading(element, text = '加载中...') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }

    if (!element) return;

    const originalContent = element.innerHTML;
    element.dataset.originalContent = originalContent;

    element.innerHTML = `
        <div class="flex items-center justify-center">
            <div class="loading-spinner mr-2"></div>
            <span>${text}</span>
        </div>
    `;

    element.disabled = true;
}

function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }

    if (!element || !element.dataset.originalContent) return;

    element.innerHTML = element.dataset.originalContent;
    element.disabled = false;
    delete element.dataset.originalContent;
}

function confirmAction(message, callback) {
    // 可以替换为更美观的确认对话框
    if (confirm(message)) {
        callback();
    }
}

// ===== 🎪 页面生命周期 =====
window.addEventListener('beforeunload', function() {
    // 清理资源
    document.querySelectorAll('.loading-spinner').forEach(spinner => {
        spinner.remove();
    });
});

// 导出全局函数供模板使用
window.showModal = showModal;
window.hideModal = hideModal;
window.showNotification = showNotification;
window.hideNotification = hideNotification;
window.copyToClipboard = copyToClipboard;
window.smoothScrollTo = smoothScrollTo;
window.makeRequest = makeRequest;
window.showLoading = showLoading;
window.hideLoading = hideLoading;

// ===== 📱 高级响应式功能 =====
function initializeResponsiveFeatures() {
    // 检测设备类型
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
    const isDesktop = window.innerWidth > 1024;

    // 设置设备类型类名
    document.body.classList.add(
        isMobile ? 'device-mobile' :
        isTablet ? 'device-tablet' :
        'device-desktop'
    );

    // 移动端特殊处理
    if (isMobile) {
        initializeMobileFeatures();
    }

    // 平板特殊处理
    if (isTablet) {
        initializeTabletFeatures();
    }

    // 桌面端特殊处理
    if (isDesktop) {
        initializeDesktopFeatures();
    }
}

function initializeMobileFeatures() {
    // 移动端触摸优化
    document.addEventListener('touchstart', function() {}, { passive: true });

    // 移动端滚动优化
    let ticking = false;
    function updateOnScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                // 移动端滚动处理
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', updateOnScroll, { passive: true });

    // 移动端导航优化
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            mobileMenu.classList.toggle('animate-slide-down');
        });
    }

    // 移动端表单优化
    document.querySelectorAll('input[type="text"], input[type="email"], input[type="url"], textarea').forEach(input => {
        input.addEventListener('focus', function() {
            // 防止iOS Safari缩放
            this.style.fontSize = '16px';
        });
    });
}

function initializeTabletFeatures() {
    // 平板端特殊功能
    document.body.classList.add('tablet-optimized');

    // 平板端手势支持
    let startX, startY, endX, endY;

    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    }, { passive: true });

    document.addEventListener('touchend', function(e) {
        endX = e.changedTouches[0].clientX;
        endY = e.changedTouches[0].clientY;
        handleSwipe();
    }, { passive: true });

    function handleSwipe() {
        const deltaX = endX - startX;
        const deltaY = endY - startY;

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > 50) {
                // 右滑
                console.log('Swipe right');
            } else if (deltaX < -50) {
                // 左滑
                console.log('Swipe left');
            }
        }
    }
}

function initializeDesktopFeatures() {
    // 桌面端特殊功能
    document.body.classList.add('desktop-optimized');

    // 桌面端键盘快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K 打开搜索
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // ESC 关闭模态框
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal-container:not(.hidden)');
            if (openModal) {
                hideModal(openModal.id);
            }
        }
    });

    // 桌面端鼠标跟踪效果
    let mouseX = 0, mouseY = 0;

    document.addEventListener('mousemove', function(e) {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // 更新鼠标跟踪元素
        document.querySelectorAll('.mouse-follow').forEach(element => {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            const deltaX = (mouseX - centerX) * 0.1;
            const deltaY = (mouseY - centerY) * 0.1;

            element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
        });
    });
}

// ===== 🎭 高级动画控制器 =====
class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animations = new Map();
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
        this.setupScrollAnimations();
        this.setupHoverAnimations();
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.triggerAnimation(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 观察所有动画元素
        document.querySelectorAll('[data-animate]').forEach(element => {
            observer.observe(element);
        });

        this.observers.set('intersection', observer);
    }

    setupScrollAnimations() {
        let ticking = false;

        const updateScrollAnimations = () => {
            const scrolled = window.pageYOffset;
            const windowHeight = window.innerHeight;

            document.querySelectorAll('[data-scroll-animate]').forEach(element => {
                const rect = element.getBoundingClientRect();
                const elementTop = rect.top + scrolled;
                const elementHeight = rect.height;

                const startAnimation = elementTop - windowHeight;
                const endAnimation = elementTop + elementHeight;

                if (scrolled >= startAnimation && scrolled <= endAnimation) {
                    const progress = (scrolled - startAnimation) / (endAnimation - startAnimation);
                    this.updateScrollAnimation(element, progress);
                }
            });

            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        }, { passive: true });
    }

    setupHoverAnimations() {
        document.querySelectorAll('[data-hover-animate]').forEach(element => {
            element.addEventListener('mouseenter', () => {
                this.triggerHoverAnimation(element, 'enter');
            });

            element.addEventListener('mouseleave', () => {
                this.triggerHoverAnimation(element, 'leave');
            });
        });
    }

    triggerAnimation(element) {
        const animationType = element.dataset.animate;
        const delay = parseInt(element.dataset.animateDelay) || 0;

        setTimeout(() => {
            element.classList.add(`animate-${animationType}`);
            element.classList.add('animation-complete');
        }, delay);
    }

    updateScrollAnimation(element, progress) {
        const animationType = element.dataset.scrollAnimate;

        switch (animationType) {
            case 'parallax':
                const speed = parseFloat(element.dataset.parallaxSpeed) || 0.5;
                element.style.transform = `translateY(${progress * 100 * speed}px)`;
                break;

            case 'fade':
                element.style.opacity = Math.max(0, Math.min(1, progress * 2));
                break;

            case 'scale':
                const scale = 0.8 + (progress * 0.2);
                element.style.transform = `scale(${scale})`;
                break;
        }
    }

    triggerHoverAnimation(element, type) {
        const animationType = element.dataset.hoverAnimate;

        if (type === 'enter') {
            element.classList.add(`hover-${animationType}`);
        } else {
            element.classList.remove(`hover-${animationType}`);
        }
    }
}

// ===== 🔧 性能监控和优化 =====
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        this.measurePageLoad();
        this.monitorFPS();
        this.optimizeImages();
    }

    measurePageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
            this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

            console.log('Performance Metrics:', this.metrics);
        });
    }

    monitorFPS() {
        let lastTime = performance.now();
        let frames = 0;

        const measureFPS = (currentTime) => {
            frames++;

            if (currentTime >= lastTime + 1000) {
                this.metrics.fps = Math.round((frames * 1000) / (currentTime - lastTime));
                frames = 0;
                lastTime = currentTime;

                // 如果FPS过低，启用性能优化
                if (this.metrics.fps < 30) {
                    this.enablePerformanceMode();
                }
            }

            requestAnimationFrame(measureFPS);
        };

        requestAnimationFrame(measureFPS);
    }

    optimizeImages() {
        // 懒加载图片
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            img.classList.add('lazy');
            imageObserver.observe(img);
        });
    }

    enablePerformanceMode() {
        document.body.classList.add('performance-mode');

        // 减少动画
        document.querySelectorAll('.animate-float, .animate-pulse').forEach(element => {
            element.style.animationDuration = '10s';
        });

        // 禁用复杂效果
        document.querySelectorAll('.backdrop-blur-lg, .backdrop-blur-xl').forEach(element => {
            element.classList.remove('backdrop-blur-lg', 'backdrop-blur-xl');
            element.classList.add('backdrop-blur-sm');
        });
    }
}

// ===== 🚀 初始化所有高级功能 =====
document.addEventListener('DOMContentLoaded', function() {
    // 初始化响应式功能
    initializeResponsiveFeatures();

    // 初始化动画控制器
    window.animationController = new AnimationController();

    // 初始化性能监控
    window.performanceMonitor = new PerformanceMonitor();

    // 窗口大小变化时重新初始化响应式功能
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            initializeResponsiveFeatures();
        }, 250);
    });
});
function initializeCounterAnimations() {
    const counters = $('.stats-value');

    const animateCounter = (counter) => {
        const target = parseInt(counter.text().replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.text(Math.floor(current));
        }, 16);
    };

    // 使用Intersection Observer触发动画
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter($(entry.target));
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.each(function() {
            observer.observe(this);
        });
    }
}

// 滚动效果
function initializeScrollEffects() {
    // 滚动显示动画
    const animateOnScroll = () => {
        $('.animate-on-scroll').each(function() {
            const element = $(this);
            const elementTop = element.offset().top;
            const elementBottom = elementTop + element.outerHeight();
            const viewportTop = $(window).scrollTop();
            const viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                element.addClass('animate-visible');
            }
        });
    };

    $(window).on('scroll', throttle(animateOnScroll, 100));
    animateOnScroll(); // 初始检查

    // 滚动进度条
    const updateScrollProgress = () => {
        const scrollTop = $(window).scrollTop();
        const docHeight = $(document).height() - $(window).height();
        const scrollPercent = (scrollTop / docHeight) * 100;

        $('.scroll-progress').css('width', scrollPercent + '%');
    };

    $(window).on('scroll', throttle(updateScrollProgress, 50));
}

// 视差效果
function initializeParallax() {
    $(window).on('scroll', throttle(() => {
        const scrolled = $(window).scrollTop();

        $('.parallax-bg').each(function() {
            const rate = scrolled * -0.5;
            $(this).css('transform', `translateY(${rate}px)`);
        });

        $('.parallax-element').each(function() {
            const rate = scrolled * -0.3;
            $(this).css('transform', `translateY(${rate}px)`);
        });
    }, 16));
}

// 进度条动画
function initializeProgressBars() {
    $('.progress-bar').each(function() {
        const progressBar = $(this);
        const targetWidth = progressBar.data('progress') || 0;

        // 使用Intersection Observer触发动画
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        progressBar.find('.progress-fill').animate({
                            width: targetWidth + '%'
                        }, 1500, 'easeOutCubic');
                        observer.unobserve(entry.target);
                    }
                });
            });

            observer.observe(this);
        }
    });
}

// 工具提示
function initializeTooltips() {
    // 简单的工具提示实现
    $('[data-tooltip]').hover(
        function() {
            const tooltip = $(this).data('tooltip');
            const tooltipElement = $(`<div class="tooltip-popup">${tooltip}</div>`);

            $('body').append(tooltipElement);

            const rect = this.getBoundingClientRect();
            tooltipElement.css({
                top: rect.top - tooltipElement.outerHeight() - 10,
                left: rect.left + (rect.width / 2) - (tooltipElement.outerWidth() / 2)
            });

            tooltipElement.fadeIn(200);
        },
        function() {
            $('.tooltip-popup').fadeOut(200, function() {
                $(this).remove();
            });
        }
    );
}

// 页面加载动画
function showPageLoading() {
    if (!$('.page-loading').length) {
        const loadingHtml = `
            <div class="page-loading">
                <div class="loading-spinner-lg"></div>
                <p class="mt-4 text-gray-600">页面加载中...</p>
            </div>
        `;
        $('body').append(loadingHtml);
    }
    $('.page-loading').fadeIn(300);
}

function hidePageLoading() {
    $('.page-loading').fadeOut(300, function() {
        $(this).remove();
    });
}

// 通知系统增强
function initializeNotifications() {
    // 自动隐藏通知
    $('.notification').each(function() {
        const notification = $(this);
        setTimeout(() => {
            notification.addClass('notification-hide');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    });
}

function hideNotifications() {
    $('.notification').addClass('notification-hide');
    setTimeout(() => {
        $('.notification').remove();
    }, 300);
}

// 动画系统
function initializeAnimations() {
    // 为元素添加进入动画
    $('.animate-fade-in, .animate-slide-up, .animate-slide-down, .animate-scale-in, .animate-bounce-in').each(function() {
        $(this).addClass('animate-on-scroll');
    });

    // 交错动画
    $('.stagger-animation').children().each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });
}

// 无障碍功能
function initializeAccessibility() {
    // 键盘导航
    $(document).on('keydown', function(e) {
        // ESC键关闭模态框
        if (e.key === 'Escape') {
            $('.modal-container:visible').each(function() {
                hideModal(this.id);
            });
        }

        // Tab键焦点管理
        if (e.key === 'Tab') {
            const focusableElements = $('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            const firstElement = focusableElements.first();
            const lastElement = focusableElements.last();

            if (e.shiftKey && document.activeElement === firstElement[0]) {
                e.preventDefault();
                lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement[0]) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    });

    // 焦点指示器
    $('button, a, input, select, textarea').on('focus', function() {
        $(this).addClass('focus-visible');
    }).on('blur', function() {
        $(this).removeClass('focus-visible');
    });
}

// 模态框增强功能
function showModal(modalId) {
    const modal = $('#' + modalId);
    if (modal.length) {
        modal.removeClass('hidden').addClass('animate-scale-in');
        $('body').addClass('modal-open');

        // 焦点管理
        const firstFocusable = modal.find('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])').first();
        firstFocusable.focus();

        // 背景点击关闭
        modal.find('.modal-backdrop').on('click', function() {
            hideModal(modalId);
        });
    }
}

function hideModal(modalId) {
    const modal = $('#' + modalId);
    if (modal.length) {
        modal.removeClass('animate-scale-in');
        setTimeout(() => {
            modal.addClass('hidden');
            $('body').removeClass('modal-open');
        }, 300);
    }
}

// 平滑滚动
function smoothScrollTo(target, duration = 800) {
    const targetElement = $(target);
    if (targetElement.length) {
        $('html, body').animate({
            scrollTop: targetElement.offset().top - 100
        }, duration, 'easeInOutCubic');
    }
}

// 自定义缓动函数
$.easing.easeInOutCubic = function(x, t, b, c, d) {
    if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
    return c / 2 * ((t -= 2) * t * t + 2) + b;
};

$.easing.easeOutCubic = function(x, t, b, c, d) {
    return c * ((t = t / d - 1) * t * t + 1) + b;
};
