/* 🎨 现代化移动应用级UI设计系统 */

/* ===== 🎯 设计令牌系统 ===== */
:root {
  /* 🌈 高级色彩系统 */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  --color-secondary-50: #fdf4ff;
  --color-secondary-100: #fae8ff;
  --color-secondary-200: #f5d0fe;
  --color-secondary-300: #f0abfc;
  --color-secondary-400: #e879f9;
  --color-secondary-500: #d946ef;
  --color-secondary-600: #c026d3;
  --color-secondary-700: #a21caf;
  --color-secondary-800: #86198f;
  --color-secondary-900: #701a75;

  --color-accent-50: #fff7ed;
  --color-accent-100: #ffedd5;
  --color-accent-200: #fed7aa;
  --color-accent-300: #fdba74;
  --color-accent-400: #fb923c;
  --color-accent-500: #f97316;
  --color-accent-600: #ea580c;
  --color-accent-700: #c2410c;
  --color-accent-800: #9a3412;
  --color-accent-900: #7c2d12;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  /* 🌙 深色模式色彩 */
  --color-dark-50: #f8fafc;
  --color-dark-100: #f1f5f9;
  --color-dark-200: #e2e8f0;
  --color-dark-300: #cbd5e1;
  --color-dark-400: #94a3b8;
  --color-dark-500: #64748b;
  --color-dark-600: #475569;
  --color-dark-700: #334155;
  --color-dark-800: #1e293b;
  --color-dark-900: #0f172a;

  /* ✨ 高级阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* 🎨 渐变阴影 */
  --shadow-colored-primary: 0 10px 15px -3px rgba(14, 165, 233, 0.1), 0 4px 6px -2px rgba(14, 165, 233, 0.05);
  --shadow-colored-secondary: 0 10px 15px -3px rgba(217, 70, 239, 0.1), 0 4px 6px -2px rgba(217, 70, 239, 0.05);
  --shadow-colored-success: 0 10px 15px -3px rgba(34, 197, 94, 0.1), 0 4px 6px -2px rgba(34, 197, 94, 0.05);

  /* 🔄 圆角系统 */
  --radius-none: 0;
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* 📏 间距系统 */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;

  /* 🎭 动画系统 */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* 📱 移动应用级别的Z-index系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ===== 🎯 高级按钮组件系统 ===== */
.btn {
  @apply inline-flex items-center justify-center font-semibold transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden select-none;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  letter-spacing: 0.025em;
  position: relative;
  transform: translateZ(0); /* 硬件加速 */
}

/* 🌟 按钮光泽效果 */
.btn::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 opacity-0 transition-all duration-500;
  border-radius: inherit;
  transform: translateX(-100%);
}

.btn:hover::before {
  @apply opacity-100;
  transform: translateX(100%);
}

/* 🎭 按钮状态动画 */
.btn:hover {
  transform: translateY(-1px) scale(1.02);
}

.btn:active {
  transform: translateY(0) scale(0.98);
  transition-duration: var(--duration-100);
}

.btn:focus {
  @apply ring-2 ring-offset-2;
}

/* 📏 按钮尺寸系统 */
.btn-xs {
  @apply px-2 py-1 text-xs;
  min-height: 1.75rem;
  border-radius: var(--radius-md);
}

.btn-sm {
  @apply px-3 py-1.5 text-sm;
  min-height: 2rem;
  border-radius: var(--radius-lg);
}

.btn {
  @apply px-4 py-2.5 text-sm;
  min-height: 2.5rem;
  border-radius: var(--radius-xl);
}

.btn-lg {
  @apply px-6 py-3 text-base;
  min-height: 3rem;
  border-radius: var(--radius-xl);
}

.btn-xl {
  @apply px-8 py-4 text-lg;
  min-height: 3.5rem;
  border-radius: var(--radius-2xl);
}

/* 🎨 按钮变体系统 */
.btn-primary {
  @apply bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 text-white border-0;
  box-shadow: var(--shadow-colored-primary), var(--shadow-md);
}

.btn-primary:hover {
  @apply from-blue-600 via-blue-700 to-purple-700;
  box-shadow: var(--shadow-colored-primary), var(--shadow-lg);
}

.btn-primary:focus {
  @apply ring-blue-500/50;
}

.btn-secondary {
  @apply bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 text-white border-0;
  box-shadow: var(--shadow-colored-secondary), var(--shadow-md);
}

.btn-secondary:hover {
  @apply from-purple-600 via-pink-600 to-red-600;
  box-shadow: var(--shadow-colored-secondary), var(--shadow-lg);
}

.btn-secondary:focus {
  @apply ring-purple-500/50;
}

.btn-success {
  @apply bg-gradient-to-r from-emerald-500 to-green-600 text-white border-0;
  box-shadow: var(--shadow-colored-success), var(--shadow-md);
}

.btn-success:hover {
  @apply from-emerald-600 to-green-700;
  box-shadow: var(--shadow-colored-success), var(--shadow-lg);
}

.btn-success:focus {
  @apply ring-emerald-500/50;
}

.btn-warning {
  @apply bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0;
  box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.1), var(--shadow-md);
}

.btn-warning:hover {
  @apply from-amber-600 to-orange-600;
  box-shadow: 0 10px 15px -3px rgba(245, 158, 11, 0.2), var(--shadow-lg);
}

.btn-warning:focus {
  @apply ring-amber-500/50;
}

.btn-danger {
  @apply bg-gradient-to-r from-red-500 to-pink-600 text-white border-0;
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.1), var(--shadow-md);
}

.btn-danger:hover {
  @apply from-red-600 to-pink-700;
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.2), var(--shadow-lg);
}

.btn-danger:focus {
  @apply ring-red-500/50;
}

/* 🔘 轮廓按钮 */
.btn-outline {
  @apply bg-transparent border-2 border-gray-300 text-gray-700;
  box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
  @apply bg-gray-50 border-gray-400;
  box-shadow: var(--shadow-md);
}

.btn-outline-primary {
  @apply bg-transparent border-2 border-blue-500 text-blue-600;
  box-shadow: var(--shadow-sm);
}

.btn-outline-primary:hover {
  @apply bg-blue-50 border-blue-600 text-blue-700;
  box-shadow: var(--shadow-md);
}

/* 👻 幽灵按钮 */
.btn-ghost {
  @apply bg-transparent text-gray-600 border-0;
  box-shadow: none;
}

.btn-ghost:hover {
  @apply bg-gray-100 text-gray-800;
  box-shadow: var(--shadow-sm);
}

.btn-ghost-primary {
  @apply bg-transparent text-blue-600 border-0;
  box-shadow: none;
}

.btn-ghost-primary:hover {
  @apply bg-blue-50 text-blue-700;
  box-shadow: var(--shadow-sm);
}

/* ===== 🃏 高级卡片组件系统 ===== */
.card {
  @apply bg-white border border-gray-200 transition-all duration-300 ease-out;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  position: relative;
  transform: translateZ(0); /* 硬件加速 */
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px) scale(1.01);
}

/* 🌟 卡片变体 */
.card-hover {
  @apply transition-all duration-300 ease-out cursor-pointer;
}

.card-hover:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px) scale(1.02);
}

.card-glass {
  @apply bg-white/80 backdrop-blur-lg border border-white/20;
  box-shadow: var(--shadow-lg), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-gradient {
  @apply bg-gradient-to-br from-white via-blue-50/50 to-purple-50/50;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 🖼️ 卡片结构 */
.card-header {
  @apply px-6 py-4 border-b border-gray-100;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-100 bg-gray-50/50;
}

.card-image {
  @apply relative overflow-hidden;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.card-image img {
  @apply w-full h-full object-cover transition-transform duration-500;
}

.card:hover .card-image img {
  transform: scale(1.05);
}

/* 📊 统计卡片 */
.stats-card {
  @apply bg-gradient-to-br from-white to-gray-50/50 border border-gray-200/50;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  @apply absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500;
}

.stats-value {
  @apply text-3xl font-bold text-gray-900 mb-1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-label {
  @apply text-sm font-medium text-gray-600 mb-2;
}

.stats-trend {
  @apply flex items-center text-xs;
}

.trend-up {
  @apply text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full flex items-center;
}

.trend-down {
  @apply text-red-600 bg-red-50 px-2 py-1 rounded-full flex items-center;
}

.stats-icon {
  @apply w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center;
}

/* 🎯 项目卡片 */
.project-card {
  @apply bg-white border border-gray-200/50 transition-all duration-500 ease-out group;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  position: relative;
}

.project-card::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 transition-opacity duration-500;
  border-radius: inherit;
}

.project-card:hover::before {
  @apply opacity-100;
}

.project-card:hover {
  box-shadow: var(--shadow-2xl);
  transform: translateY(-6px) scale(1.02);
  border-color: rgba(59, 130, 246, 0.2);
}

.project-icon {
  @apply h-32 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 flex items-center justify-center relative overflow-hidden;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
}

.status-badge {
  @apply absolute top-4 right-4;
}

/* 🏷️ 徽章系统 */
.badge {
  @apply inline-flex items-center font-medium transition-all duration-200;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  line-height: 1;
}

.badge-sm {
  @apply px-2 py-1 text-xs;
}

.badge {
  @apply px-2.5 py-1.5 text-xs;
}

.badge-lg {
  @apply px-3 py-2 text-sm;
}

.badge-solid {
  @apply text-white;
}

.badge-outline {
  @apply bg-transparent border;
}

.badge-soft {
  @apply border-0;
}

.badge-success {
  @apply bg-emerald-500 text-white;
}

.badge-success.badge-outline {
  @apply bg-transparent border-emerald-500 text-emerald-600;
}

.badge-success.badge-soft {
  @apply bg-emerald-50 text-emerald-700;
}

.badge-warning {
  @apply bg-amber-500 text-white;
}

.badge-warning.badge-outline {
  @apply bg-transparent border-amber-500 text-amber-600;
}

.badge-warning.badge-soft {
  @apply bg-amber-50 text-amber-700;
}

.badge-danger {
  @apply bg-red-500 text-white;
}

.badge-danger.badge-outline {
  @apply bg-transparent border-red-500 text-red-600;
}

.badge-danger.badge-soft {
  @apply bg-red-50 text-red-700;
}

.badge-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ===== 📝 高级表单组件系统 ===== */
.form-group {
  @apply mb-6;
}

.form-label {
  @apply block text-sm font-semibold text-gray-700 mb-2;
}

.form-label.required::after {
  content: ' *';
  @apply text-red-500;
}

.form-input {
  @apply w-full px-4 py-3 border border-gray-300 transition-all duration-300 ease-out focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500;
  border-radius: var(--radius-xl);
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
}

.form-input:hover {
  border-color: #9ca3af;
  box-shadow: var(--shadow-md);
}

.form-input:focus {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-lg), 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-input-icon {
  @apply relative;
}

.form-input-icon .form-input {
  @apply pl-12;
}

.input-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none;
}

.form-help {
  @apply mt-2 text-xs text-gray-500;
}

.form-error {
  @apply mt-2 text-xs text-red-600;
}

/* 🔍 搜索输入框 */
.search-input {
  @apply relative;
}

.search-input .form-input {
  @apply pl-12 pr-4 py-4 text-lg bg-white/90 backdrop-blur-lg border-2 border-gray-200/50;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
}

.search-input .form-input:focus {
  @apply border-blue-500/50 bg-white;
  box-shadow: var(--shadow-xl), 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.search-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400 pointer-events-none;
}

/* ===== 🎭 动画系统 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 🎬 动画类 */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 🎯 悬停效果 */
.hover-lift {
  @apply transition-all duration-300 ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.hover-glow {
  @apply transition-all duration-300 ease-out;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), var(--shadow-lg);
}

/* 🌊 波纹效果 */
.ripple {
  @apply relative overflow-hidden;
}

.ripple::before {
  content: '';
  @apply absolute inset-0 bg-white opacity-0 transition-opacity duration-300;
  border-radius: inherit;
}

.ripple:active::before {
  @apply opacity-20;
  animation: ripple-effect 0.6s ease-out;
}

@keyframes ripple-effect {
  0% {
    transform: scale(0);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* ===== 🪟 模态框组件系统 ===== */
.modal-container {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.modal-backdrop {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 opacity-0;
}

.modal-panel {
  @apply relative bg-white mx-auto my-8 transition-all duration-300 opacity-0 translate-y-4;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

.modal-header {
  @apply px-6 py-4 border-b border-gray-100;
}

.modal-title {
  @apply text-xl font-bold text-gray-900;
}

.modal-body {
  @apply px-6 py-6;
}

.modal-footer {
  @apply px-6 py-4 border-t border-gray-100 bg-gray-50/50;
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
}

/* ===== 🔔 通知组件系统 ===== */
.notification {
  @apply px-4 py-3 rounded-xl shadow-lg border-l-4 transition-all duration-300 ease-out;
  backdrop-filter: blur(10px);
  max-width: 400px;
}

.notification-success {
  @apply bg-emerald-50/90 border-emerald-500 text-emerald-800;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(34, 197, 94, 0.1);
}

.notification-error {
  @apply bg-red-50/90 border-red-500 text-red-800;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(239, 68, 68, 0.1);
}

.notification-warning {
  @apply bg-amber-50/90 border-amber-500 text-amber-800;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(245, 158, 11, 0.1);
}

.notification-info {
  @apply bg-blue-50/90 border-blue-500 text-blue-800;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* ===== 🧭 导航组件系统 ===== */
.nav-link {
  @apply inline-flex items-center px-3 py-2 text-sm font-medium transition-all duration-200 ease-out;
  border-radius: var(--radius-lg);
}

.nav-link:hover {
  @apply bg-gray-100 text-gray-900;
  transform: translateY(-1px);
}

.nav-link.active {
  @apply bg-blue-100 text-blue-700;
  box-shadow: var(--shadow-sm);
}

/* ===== 🎨 特殊效果 ===== */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 🌟 英雄区域 */
.hero-section {
  @apply relative min-h-screen flex items-center justify-center overflow-hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section::before {
  content: '';
  @apply absolute inset-0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

/* 📱 移动端优化 */
@media (max-width: 768px) {
  .btn-xl {
    @apply px-6 py-3 text-base;
    min-height: 3rem;
  }

  .hero-section {
    @apply min-h-screen px-4;
  }

  .modal-panel {
    @apply mx-4 my-4;
    max-height: calc(100vh - 2rem);
  }

  .card {
    border-radius: var(--radius-xl);
  }

  .project-card:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

/* ===== 🎯 加载状态 ===== */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full;
  animation: spin 1s linear infinite;
}

.loading-skeleton {
  @apply bg-gray-200 animate-pulse;
  border-radius: var(--radius-md);
}

/* ===== 🔄 滚动条样式 ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300;
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* ===== 🌙 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }

  .dark .form-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }

  .dark .modal-panel {
    @apply bg-gray-800;
  }

  .dark .notification-success {
    @apply bg-emerald-900/50 text-emerald-200;
  }

  .dark .notification-error {
    @apply bg-red-900/50 text-red-200;
  }

  .dark .notification-warning {
    @apply bg-amber-900/50 text-amber-200;
  }

  .dark .notification-info {
    @apply bg-blue-900/50 text-blue-200;
  }
}

/* ===== 🎪 滚动进度条 ===== */
.scroll-progress {
  @apply fixed top-0 left-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transition-all duration-150 ease-out;
  width: 0%;
  z-index: var(--z-fixed);
}

/* ===== 🎨 实用工具类 ===== */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(12px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(16px);
}

/* ===== 📱 高级响应式设计系统 ===== */

/* 🎯 容器系统 */
.container-fluid {
  width: 100%;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

/* 响应式容器断点 */
@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* 🎨 响应式字体系统 */
.text-responsive-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-responsive-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-responsive-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-responsive-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-responsive-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  .text-responsive-xs { font-size: 0.875rem; line-height: 1.25rem; }
  .text-responsive-sm { font-size: 1rem; line-height: 1.5rem; }
  .text-responsive-base { font-size: 1.125rem; line-height: 1.75rem; }
  .text-responsive-lg { font-size: 1.25rem; line-height: 1.75rem; }
  .text-responsive-xl { font-size: 1.5rem; line-height: 2rem; }
}

@media (min-width: 1024px) {
  .text-responsive-xs { font-size: 1rem; line-height: 1.5rem; }
  .text-responsive-sm { font-size: 1.125rem; line-height: 1.75rem; }
  .text-responsive-base { font-size: 1.25rem; line-height: 1.75rem; }
  .text-responsive-lg { font-size: 1.5rem; line-height: 2rem; }
  .text-responsive-xl { font-size: 1.875rem; line-height: 2.25rem; }
}

/* 📱 移动端优化 */
@media (max-width: 767px) {
  /* 移动端按钮优化 */
  .btn {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    padding: var(--spacing-3) var(--spacing-4);
  }

  .btn-sm {
    min-height: 40px;
    padding: var(--spacing-2) var(--spacing-3);
  }

  .btn-lg {
    min-height: 48px;
    padding: var(--spacing-4) var(--spacing-6);
  }

  /* 移动端卡片优化 */
  .card {
    margin-bottom: var(--spacing-4);
    border-radius: var(--radius-xl);
  }

  .project-card {
    margin-bottom: var(--spacing-6);
  }

  /* 移动端表单优化 */
  .form-input {
    min-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端导航优化 */
  .nav-link {
    padding: var(--spacing-3) var(--spacing-4);
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  /* 移动端模态框优化 */
  .modal-panel {
    margin: var(--spacing-4);
    max-height: calc(100vh - 2rem);
    width: calc(100vw - 2rem);
  }

  /* 移动端统计卡片优化 */
  .stats-card {
    padding: var(--spacing-4);
  }

  .stats-value {
    font-size: 2rem;
  }

  /* 移动端英雄区域优化 */
  .hero-section {
    min-height: 80vh;
    padding: var(--spacing-8) var(--spacing-4);
  }

  /* 移动端搜索优化 */
  .search-input .form-input {
    padding: var(--spacing-4);
    font-size: 16px;
  }
}

/* 🖥️ 桌面端优化 */
@media (min-width: 1024px) {
  /* 桌面端悬停效果增强 */
  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-2xl);
  }

  /* 桌面端卡片间距优化 */
  .project-card {
    margin-bottom: var(--spacing-8);
  }

  /* 桌面端按钮组优化 */
  .btn-group {
    display: flex;
    gap: var(--spacing-4);
  }

  /* 桌面端导航增强 */
  .nav-link:hover {
    transform: translateY(-1px);
  }
}

/* ===== 🎭 高级动画系统 ===== */

/* 🌊 页面转场动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease-in, transform 0.3s ease-in;
}

/* 🎨 元素进入动画 */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 🔄 旋转动画 */
.animate-spin-slow {
  animation: spin 3s linear infinite;
}

.animate-spin-fast {
  animation: spin 0.5s linear infinite;
}

/* 📈 数字计数动画 */
.animate-count-up {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 🎪 特殊效果动画 */
.animate-wiggle {
  animation: wiggle 1s ease-in-out infinite;
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

/* 💫 闪烁动画 */
.animate-flash {
  animation: flash 2s infinite;
}

@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.5;
  }
}

/* 🌈 彩虹动画 */
.animate-rainbow {
  animation: rainbow 3s ease-in-out infinite;
}

@keyframes rainbow {
  0% { color: #ff0000; }
  16.66% { color: #ff8000; }
  33.33% { color: #ffff00; }
  50% { color: #00ff00; }
  66.66% { color: #0080ff; }
  83.33% { color: #8000ff; }
  100% { color: #ff0000; }
}

.btn-secondary:focus {
  @apply ring-gray-500;
}

.btn-success {
  @apply bg-gradient-to-r from-emerald-500 to-green-600 text-white;
  box-shadow: var(--shadow-medium), 0 0 0 1px rgba(16, 185, 129, 0.1);
}

.btn-success:hover {
  @apply from-emerald-600 to-green-700;
  box-shadow: var(--shadow-large);
  transform: translateY(-1px);
}

.btn-warning {
  @apply bg-gradient-to-r from-amber-500 to-orange-600 text-white;
  box-shadow: var(--shadow-medium), 0 0 0 1px rgba(245, 158, 11, 0.1);
}

.btn-warning:hover {
  @apply from-amber-600 to-orange-700;
  box-shadow: var(--shadow-large);
  transform: translateY(-1px);
}

.btn-danger {
  @apply bg-gradient-to-r from-red-500 to-rose-600 text-white;
  box-shadow: var(--shadow-medium), 0 0 0 1px rgba(239, 68, 68, 0.1);
}

.btn-danger:hover {
  @apply from-red-600 to-rose-700;
  box-shadow: var(--shadow-large);
  transform: translateY(-1px);
}

/* 幽灵按钮 */
.btn-ghost {
  @apply bg-transparent text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  box-shadow: none;
}

/* 轮廓按钮 */
.btn-outline {
  @apply bg-transparent border-2 border-current;
  box-shadow: none;
}

.btn-outline-primary {
  @apply text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white;
}

/* Hero区域按钮特殊样式 */
.hero-section .btn-secondary {
  @apply bg-white/90 text-blue-700 border-white/30 backdrop-blur-sm;
  box-shadow: var(--shadow-large), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.hero-section .btn-secondary:hover {
  @apply bg-white text-blue-800;
  box-shadow: var(--shadow-xl);
}

.hero-section .btn-primary {
  @apply bg-white/10 text-white border-2 border-white/30 backdrop-blur-sm;
  box-shadow: var(--shadow-large), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.hero-section .btn-primary:hover {
  @apply bg-white text-blue-600;
  box-shadow: var(--shadow-xl);
}

/* ===== 卡片组件系统 ===== */
.card {
  @apply bg-white overflow-hidden transition-all duration-300 ease-out;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-soft);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-hover {
  @apply hover:shadow-lg hover:-translate-y-2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  box-shadow: var(--shadow-xl);
  border-color: rgba(59, 130, 246, 0.1);
}

/* 卡片变体 */
.card-elevated {
  box-shadow: var(--shadow-medium);
}

.card-flat {
  @apply shadow-none border border-gray-200;
}

.card-glass {
  @apply bg-white/80 backdrop-blur-md border border-white/20;
  box-shadow: var(--shadow-large), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 卡片布局 */
.card-header {
  @apply px-6 py-5 border-b border-gray-100/80 bg-gradient-to-r from-gray-50/50 to-white;
}

.card-body {
  @apply px-6 py-5;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-100/80 bg-gradient-to-r from-gray-50/30 to-white;
}

/* 紧凑卡片 */
.card-compact .card-header {
  @apply px-4 py-3;
}

.card-compact .card-body {
  @apply px-4 py-3;
}

.card-compact .card-footer {
  @apply px-4 py-3;
}

/* 项目卡片特殊样式 */
.project-card {
  @apply relative group;
  border-radius: var(--radius-2xl);
  overflow: hidden;
}

.project-card::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 transition-opacity duration-300;
}

.project-card:hover::before {
  @apply opacity-100;
}

.project-card .card-image {
  @apply relative overflow-hidden;
  height: 12rem;
}

.project-card .card-image::after {
  content: '';
  @apply absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent;
}

.project-card:hover .card-image::after {
  @apply from-black/30;
}

.project-card .project-icon {
  @apply absolute inset-0 flex items-center justify-center text-white;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(147, 51, 234, 0.9) 100%);
}

.project-card .status-badge {
  @apply absolute top-4 right-4 z-10;
}

/* 统计卡片 */
.stats-card {
  @apply relative overflow-hidden;
  border-radius: var(--radius-xl);
}

.stats-card::before {
  content: '';
  @apply absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-600;
}

.stats-card:hover {
  @apply shadow-lg;
  transform: translateY(-2px);
}

.stats-card .stats-icon {
  @apply p-3 rounded-xl;
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-primary-100) 100%);
}

.stats-card .stats-value {
  @apply text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent;
}

.stats-card .stats-label {
  @apply text-sm font-medium text-gray-600;
}

.stats-card .stats-trend {
  @apply flex items-center mt-2 text-xs;
}

.stats-card .trend-up {
  @apply text-emerald-600;
}

.stats-card .trend-down {
  @apply text-red-600;
}

.stats-card .trend-neutral {
  @apply text-gray-500;
}

/* ===== 表单组件系统 ===== */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-semibold text-gray-700 mb-2;
}

.form-label.required::after {
  content: ' *';
  @apply text-red-500;
}

.form-input {
  @apply block w-full px-4 py-3 border border-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-soft);
  background: linear-gradient(to bottom, #ffffff 0%, #fafafa 100%);
}

.form-input:hover {
  @apply border-gray-300;
  box-shadow: var(--shadow-medium);
}

.form-input:focus {
  @apply bg-white;
  box-shadow: var(--shadow-medium), 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-input.error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500/20;
}

.form-input.success {
  @apply border-green-300 focus:border-green-500 focus:ring-green-500/20;
}

/* 输入框图标 */
.form-input-icon {
  @apply relative;
}

.form-input-icon .form-input {
  @apply pl-10;
}

.form-input-icon .input-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5;
}

.form-input-icon .form-input:focus + .input-icon {
  @apply text-blue-500;
}

/* 搜索框特殊样式 */
.search-input {
  @apply relative;
}

.search-input .form-input {
  @apply pl-12 pr-4 py-3 bg-white/80 backdrop-blur-sm border-white/20;
  border-radius: var(--radius-xl);
}

.search-input .search-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5;
}

/* 表单错误和帮助文本 */
.form-error {
  @apply mt-2 text-sm text-red-600 flex items-center;
}

.form-error::before {
  content: '⚠';
  @apply mr-1;
}

.form-help {
  @apply mt-2 text-sm text-gray-500;
}

.form-success {
  @apply mt-2 text-sm text-green-600 flex items-center;
}

.form-success::before {
  content: '✓';
  @apply mr-1;
}

/* 选择框样式 */
.form-select {
  @apply form-input appearance-none bg-no-repeat bg-right pr-10;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-size: 1.25rem 1.25rem;
}

/* 复选框和单选框 */
.form-checkbox,
.form-radio {
  @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2;
}

.form-radio {
  @apply rounded-full;
}

/* 文件上传 */
.form-file {
  @apply block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100;
}

/* ===== 导航组件系统 ===== */
.nav-link {
  @apply flex items-center px-4 py-3 text-sm font-medium transition-all duration-300 relative;
  border-radius: var(--radius-lg);
}

.nav-link::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 transition-opacity duration-300;
  border-radius: inherit;
}

.nav-link:hover::before {
  @apply opacity-100;
}

.nav-link:hover {
  @apply text-blue-700 transform translate-x-1;
}

.nav-link-active {
  @apply bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border-l-4 border-blue-500;
}

.nav-link-active::before {
  @apply opacity-100;
}

/* 面包屑导航 */
.breadcrumb {
  @apply flex items-center space-x-2 text-sm text-gray-500;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  @apply ml-2 text-gray-400;
}

.breadcrumb-item a {
  @apply text-blue-600 hover:text-blue-800 transition-colors duration-200;
}

/* ===== 徽章组件系统 ===== */
.badge {
  @apply inline-flex items-center px-3 py-1 text-xs font-semibold transition-all duration-200;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-soft);
}

.badge-sm {
  @apply px-2 py-0.5 text-xs;
}

.badge-lg {
  @apply px-4 py-2 text-sm;
}

/* 徽章变体 */
.badge-primary {
  @apply bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-200;
}

.badge-success {
  @apply bg-gradient-to-r from-emerald-100 to-green-200 text-emerald-800 border border-emerald-200;
}

.badge-warning {
  @apply bg-gradient-to-r from-amber-100 to-yellow-200 text-amber-800 border border-amber-200;
}

.badge-danger {
  @apply bg-gradient-to-r from-red-100 to-rose-200 text-red-800 border border-red-200;
}

.badge-gray {
  @apply bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-200;
}

.badge-purple {
  @apply bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border border-purple-200;
}

/* 实心徽章 */
.badge-solid.badge-primary {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-600;
}

.badge-solid.badge-success {
  @apply bg-gradient-to-r from-emerald-500 to-green-600 text-white border-emerald-600;
}

.badge-solid.badge-warning {
  @apply bg-gradient-to-r from-amber-500 to-yellow-600 text-white border-amber-600;
}

.badge-solid.badge-danger {
  @apply bg-gradient-to-r from-red-500 to-rose-600 text-white border-red-600;
}

/* 点状徽章 */
.badge-dot {
  @apply relative pl-6;
}

.badge-dot::before {
  content: '';
  @apply absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full;
}

.badge-dot.badge-primary::before {
  @apply bg-blue-500;
}

.badge-dot.badge-success::before {
  @apply bg-emerald-500;
}

.badge-dot.badge-warning::before {
  @apply bg-amber-500;
}

.badge-dot.badge-danger::before {
  @apply bg-red-500;
}

/* 脉冲徽章 */
.badge-pulse::before {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* ===== 动画和过渡效果 ===== */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 骨架屏动画 */
.skeleton {
  @apply bg-gray-200 rounded-lg animate-pulse;
}

.shimmer {
  @apply relative overflow-hidden bg-gray-200 rounded-lg;
}

.shimmer::after {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== 渐变和特效 ===== */
.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* 文本渐变 */
.text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-large), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 霓虹效果 */
.neon-blue {
  box-shadow: 0 0 5px #3b82f6, 0 0 10px #3b82f6, 0 0 15px #3b82f6, 0 0 20px #3b82f6;
}

.neon-purple {
  box-shadow: 0 0 5px #8b5cf6, 0 0 10px #8b5cf6, 0 0 15px #8b5cf6, 0 0 20px #8b5cf6;
}

/* ===== 通知组件系统 ===== */
.notification {
  @apply fixed top-6 right-6 z-50 max-w-sm p-5 transform transition-all duration-500 ease-out;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(16px);
}

.notification.animate-slide-down {
  animation: notificationSlideIn 0.5s ease-out;
}

@keyframes notificationSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.notification-success {
  @apply bg-gradient-to-r from-emerald-500 to-green-600 text-white border border-emerald-400;
}

.notification-error {
  @apply bg-gradient-to-r from-red-500 to-rose-600 text-white border border-red-400;
}

.notification-warning {
  @apply bg-gradient-to-r from-amber-500 to-orange-600 text-white border border-amber-400;
}

.notification-info {
  @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white border border-blue-400;
}

.notification .notification-icon {
  @apply flex-shrink-0 w-6 h-6;
}

.notification .notification-content {
  @apply ml-3 flex-1;
}

.notification .notification-title {
  @apply text-sm font-semibold;
}

.notification .notification-message {
  @apply text-sm opacity-90 mt-1;
}

.notification .notification-close {
  @apply ml-4 flex-shrink-0 text-white/80 hover:text-white transition-colors duration-200;
}

/* ===== 模态框组件系统 ===== */
.modal-backdrop {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300;
}

.modal-container {
  @apply fixed inset-0 z-50 overflow-y-auto flex items-center justify-center p-4;
}

.modal-panel {
  @apply relative bg-white rounded-2xl shadow-2xl transform transition-all duration-300 max-w-lg w-full;
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.modal-panel.animate-scale-in {
  animation: modalScaleIn 0.3s ease-out;
}

@keyframes modalScaleIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  @apply px-6 py-5 border-b border-gray-100;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-body {
  @apply px-6 py-5;
}

.modal-footer {
  @apply px-6 py-4 border-t border-gray-100 bg-gray-50/50 rounded-b-2xl;
}

/* 模态框关闭按钮 */
.modal-close-btn {
  @apply absolute top-4 right-4 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200;
}

.modal-close-btn:hover {
  transform: scale(1.1);
}

/* ===== 加载和状态指示器 ===== */
.loading-spinner {
  @apply inline-block w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

.loading-spinner-sm {
  @apply w-4 h-4;
}

.loading-spinner-lg {
  @apply w-6 h-6;
}

.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots .dot {
  @apply w-2 h-2 bg-blue-500 rounded-full animate-pulse;
}

.loading-dots .dot:nth-child(1) {
  animation-delay: 0ms;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: 150ms;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 300ms;
}

/* 进度条 */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full overflow-hidden;
  height: 8px;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-out;
  border-radius: inherit;
}

.progress-bar-sm {
  height: 4px;
}

.progress-bar-lg {
  height: 12px;
}

/* ===== 工具类 ===== */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.text-shadow-xl {
  text-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* 触控目标最小尺寸 */
.touch-target {
  min-width: 44px;
  min-height: 44px;
}

/* 响应式图片 */
.responsive-img {
  @apply w-full h-auto object-cover;
}

/* 截断文本 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ===== 暗色模式样式 ===== */
.dark .card {
  @apply bg-gray-800 border-gray-700;
  box-shadow: var(--shadow-medium), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.dark .card-hover:hover {
  @apply border-gray-600;
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .form-input {
  @apply bg-gray-800 border-gray-600 text-white placeholder-gray-400;
}

.dark .form-input:hover {
  @apply border-gray-500;
}

.dark .form-input:focus {
  @apply bg-gray-800 border-blue-500;
}

.dark .nav-link {
  @apply text-gray-300 hover:bg-gray-700 hover:text-white;
}

.dark .nav-link-active {
  @apply bg-blue-900/50 text-blue-300 border-blue-400;
}

.dark .modal-panel {
  @apply bg-gray-800 border border-gray-700;
}

.dark .modal-header {
  @apply border-gray-700;
}

.dark .modal-footer {
  @apply border-gray-700 bg-gray-800/50;
}

.dark .notification {
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.dark .stats-card::before {
  @apply from-blue-400 to-purple-500;
}

.dark .stats-value {
  @apply from-white to-gray-200;
}

.dark .stats-label {
  @apply text-gray-400;
}

/* ===== 响应式设计 ===== */
@media (max-width: 640px) {
  .btn-lg {
    @apply px-4 py-2.5 text-sm;
    min-height: 2.5rem;
  }

  .btn-xl {
    @apply px-6 py-3 text-base;
    min-height: 3rem;
  }

  .card-body {
    @apply px-4 py-4;
  }

  .card-header,
  .card-footer {
    @apply px-4 py-3;
  }

  .modal-panel {
    @apply mx-4 max-w-none;
  }

  .notification {
    @apply left-4 right-4 max-w-none;
  }

  .stats-card .stats-value {
    @apply text-2xl;
  }
}

@media (max-width: 768px) {
  .project-card .card-image {
    height: 10rem;
  }

  .hero-section .btn {
    @apply w-full;
  }

  .search-input .form-input {
    @apply text-base; /* 防止iOS缩放 */
  }
}

/* ===== 特殊效果和动画增强 ===== */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

.focus-ring:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.dark .focus-ring:focus {
  @apply ring-offset-gray-800;
}

/* 滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-600 hover:bg-gray-500;
}

/* 选择文本样式 */
::selection {
  @apply bg-blue-500 text-white;
}

.dark ::selection {
  @apply bg-blue-400 text-gray-900;
}

/* 焦点可见性 */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.dark .focus-visible:focus-visible {
  @apply ring-offset-gray-800;
}

/* ===== 微交互和动画增强 ===== */

/* 波纹效果 */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 页面加载状态 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.page-loaded {
  animation: pageLoadComplete 0.5s ease-out;
}

@keyframes pageLoadComplete {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动进度条 */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  z-index: 1000;
  transition: width 0.1s ease;
}

/* 工具提示 */
.tooltip-popup {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

.tooltip-popup::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

/* 输入框焦点增强 */
.input-focused .form-input {
  transform: translateY(-2px);
  box-shadow: var(--shadow-large), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.label-floating {
  transform: translateY(-20px) scale(0.85);
  color: #3b82f6;
}

/* 卡片悬停增强 */
.hover-active {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl), 0 0 30px rgba(59, 130, 246, 0.15);
}

/* 通知动画增强 */
.notification-hide {
  transform: translateX(100%) scale(0.9);
  opacity: 0;
}

/* 滚动动画 */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-visible {
  opacity: 1;
  transform: translateY(0);
}

/* 交错动画 */
.stagger-animation > * {
  opacity: 0;
  transform: translateY(20px);
  animation: staggerFadeIn 0.6s ease-out forwards;
}

@keyframes staggerFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 模态框增强 */
.modal-open {
  overflow: hidden;
}

.modal-container {
  backdrop-filter: blur(8px);
}

/* 焦点可见性增强 */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 3D变换支持 */
.project-card {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

/* 视差元素 */
.parallax-bg,
.parallax-element {
  will-change: transform;
}

/* 性能优化 */
.card-hover,
.btn,
.nav-link {
  will-change: transform, box-shadow;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    @apply shadow-none border border-gray-300;
  }

  .btn {
    @apply shadow-none;
  }

  .ripple,
  .tooltip-popup,
  .page-loading,
  .scroll-progress {
    display: none !important;
  }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .parallax-bg,
  .parallax-element {
    transform: none !important;
  }
}
