{% extends "admin/base.html" %}

{% block title %}仪表板 - 管理后台{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<!-- 🎯 现代化欢迎区域 -->
<div class="mb-8">
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-700 to-indigo-800 rounded-3xl shadow-2xl">
        <!-- 装饰性背景元素 -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full blur-3xl transform translate-x-32 -translate-y-32"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full blur-2xl transform -translate-x-24 translate-y-24"></div>
        <div class="absolute top-1/2 left-1/2 w-32 h-32 bg-white/5 rounded-full blur-xl transform -translate-x-16 -translate-y-16"></div>

        <div class="relative p-8 lg:p-12">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between">
                <div class="mb-8 lg:mb-0">
                    <div class="flex items-center mb-6">
                        <div class="w-20 h-20 bg-white/20 backdrop-blur-md rounded-3xl flex items-center justify-center mr-6 shadow-2xl">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-4xl lg:text-5xl font-black text-white mb-3 drop-shadow-lg">
                                欢迎回来，{{ current_user.name if current_user.is_authenticated else '管理员' }}！
                            </h1>
                            <p class="text-white/90 text-xl font-light">
                                管理您的项目研究报告平台
                            </p>
                        </div>
                    </div>

                    <!-- 系统状态指示器 -->
                    <div class="flex flex-wrap gap-6">
                        <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                            <div class="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                            <span class="text-white/90 font-medium">系统运行正常</span>
                        </div>
                        <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                            <svg class="w-4 h-4 mr-3 text-white/90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-white/90 font-medium">最后登录: 刚刚</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作按钮 -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ url_for('admin.reports') }}" class="btn btn-lg bg-white/20 border-white/30 text-white hover:bg-white hover:text-blue-600 backdrop-blur-md shadow-xl hover-lift">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建报告
                    </a>
                    <a href="{{ url_for('public.index') }}" target="_blank" class="btn btn-lg btn-ghost text-white/90 hover:text-white hover:bg-white/10 backdrop-blur-md">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        查看前台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 📊 现代化统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8 -mt-8 relative z-10">
    <!-- 研究报告总数 -->
    <div class="stats-card group hover-lift animate-slide-up">
        <div class="flex items-center justify-between p-8">
            <div class="flex-1">
                <div class="stats-value text-4xl font-black mb-2" data-stat="total_reports">{{ stats.total_reports or 0 }}</div>
                <div class="stats-label text-lg font-semibold mb-3">研究报告总数</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +12%
                    </span>
                    <span class="text-gray-500 ml-1">较上月增长</span>
                </div>
            </div>
            <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                <svg class="w-12 h-12 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 待处理请求 -->
    <div class="stats-card group hover-lift animate-slide-up" style="animation-delay: 0.1s;">
        <div class="flex items-center justify-between p-8">
            <div class="flex-1">
                <div class="stats-value text-4xl font-black mb-2" data-stat="pending_requests">{{ stats.pending_requests or 0 }}</div>
                <div class="stats-label text-lg font-semibold mb-3">待处理请求</div>
                <div class="stats-trend">
                    {% if stats.pending_requests and stats.pending_requests > 0 %}
                    <span class="trend-warning bg-amber-50 text-amber-700 px-3 py-1 rounded-full flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        需要处理
                    </span>
                    {% else %}
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        全部处理完成
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                <svg class="w-12 h-12 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 本月新增报告 -->
    <div class="stats-card group hover-lift animate-slide-up" style="animation-delay: 0.2s;">
        <div class="flex items-center justify-between p-8">
            <div class="flex-1">
                <div class="stats-value text-4xl font-black mb-2" data-stat="monthly_reports">{{ (stats.recent_reports | length) or 0 }}</div>
                <div class="stats-label text-lg font-semibold mb-3">本月新增报告</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +{{ (stats.recent_reports | length) or 0 }}
                    </span>
                    <span class="text-gray-500 ml-1">本月新增</span>
                </div>
            </div>
            <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                <svg class="w-12 h-12 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- 本月新增请求 -->
    <div class="stats-card group hover-lift animate-slide-up" style="animation-delay: 0.3s;">
        <div class="flex items-center justify-between p-8">
            <div class="flex-1">
                <div class="stats-value text-4xl font-black mb-2" data-stat="monthly_requests">{{ (stats.recent_requests | length) or 0 }}</div>
                <div class="stats-label text-lg font-semibold mb-3">本月新增请求</div>
                <div class="stats-trend">
                    <span class="trend-up">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        +{{ (stats.recent_requests | length) or 0 }}
                    </span>
                    <span class="text-gray-500 ml-1">本月新增</span>
                </div>
            </div>
            <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                <svg class="w-12 h-12 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- 📈 现代化图表区域 -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
    <!-- 报告趋势图表 -->
    <div class="card card-hover bg-white dark:bg-gray-800 shadow-xl border-0">
        <div class="card-header border-b border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                    <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    报告趋势分析
                </h3>
                <div class="flex space-x-2">
                    <button class="btn btn-sm bg-blue-100 text-blue-700 hover:bg-blue-200 border-0">7天</button>
                    <button class="btn btn-sm btn-ghost text-gray-500 hover:bg-gray-100">30天</button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="h-80 flex items-center justify-center">
                <div class="text-center">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400">图表数据加载中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 请求状态分布图表 -->
    <div class="card card-hover bg-white dark:bg-gray-800 shadow-xl border-0">
        <div class="card-header border-b border-gray-100 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <svg class="w-6 h-6 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                </svg>
                请求状态分布
            </h3>
        </div>
        <div class="card-body">
            <div class="h-80 flex items-center justify-center">
                <div class="text-center">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400">图表数据加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 📋 内容网格区域 -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
    <!-- 最近的报告 -->
    <div class="card card-hover bg-white dark:bg-gray-800 shadow-xl border-0">
        <div class="card-header border-b border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                    <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    最近的报告
                </h3>
                <a href="{{ url_for('admin.reports') }}" class="btn btn-sm btn-primary hover-lift">
                    查看全部 →
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if stats.recent_reports %}
                <div class="space-y-4">
                    {% for report in stats.recent_reports %}
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200 hover-lift group">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors duration-200">{{ report.project_name }}</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    创建者: {{ report.creator_name }} •
                                    {{ report.created_at.strftime('%Y-%m-%d') if report.created_at else '未知时间' }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="badge {% if report.is_published %}badge-success{% else %}badge-soft bg-gray-100 text-gray-700{% endif %}">
                                {{ '已发布' if report.is_published else '草稿' }}
                            </span>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 group-hover:scale-110 transition-transform duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 font-medium">暂无报告</p>
                    <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">创建第一个研究报告开始使用</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- 最近的请求 -->
    <div class="card card-hover bg-white dark:bg-gray-800 shadow-xl border-0">
        <div class="card-header border-b border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                    <svg class="w-6 h-6 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    最近的请求
                </h3>
                <a href="{{ url_for('admin.requests') }}" class="btn btn-sm btn-primary hover-lift">
                    查看全部 →
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if stats.recent_requests %}
                <div class="space-y-4">
                    {% for request in stats.recent_requests %}
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200 hover-lift group">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-bold text-gray-900 dark:text-white group-hover:text-purple-600 transition-colors duration-200">{{ request.project_name }}</h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ request.user_email }} •
                                    {{ request.created_at.strftime('%Y-%m-%d') if request.created_at else '未知时间' }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="badge {% if request.status == 'pending' %}badge-warning{% elif request.status == 'processing' %}badge-info bg-blue-100 text-blue-700{% elif request.status == 'completed' %}badge-success{% elif request.status == 'rejected' %}badge-danger bg-red-100 text-red-700{% else %}badge-soft{% endif %}">
                                {% if request.status == 'pending' %}待处理
                                {% elif request.status == 'processing' %}处理中
                                {% elif request.status == 'completed' %}已完成
                                {% elif request.status == 'rejected' %}已拒绝
                                {% else %}{{ request.status }}
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 font-medium">暂无请求</p>
                    <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">等待用户提交项目分析请求</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- ⚡ 快速操作区域 -->
<div class="mb-8">
    <div class="card card-hover bg-white dark:bg-gray-800 shadow-xl border-0">
        <div class="card-header border-b border-gray-100 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <svg class="w-6 h-6 mr-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                快速操作
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                <a href="{{ url_for('admin.reports') }}" class="btn btn-lg btn-primary hover-lift group">
                    <svg class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    添加新报告
                </a>
                <a href="{{ url_for('admin.reports') }}" class="btn btn-lg btn-outline hover-lift group">
                    <svg class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                    管理报告
                </a>
                <a href="{{ url_for('admin.requests') }}" class="btn btn-lg btn-warning hover-lift group">
                    <svg class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    处理请求
                </a>
                <a href="{{ url_for('public.index') }}" target="_blank" class="btn btn-lg btn-ghost hover-lift group">
                    <svg class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                    查看网站
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 🔧 系统状态区域 -->
<div class="mb-8">
    <div class="card card-hover bg-white dark:bg-gray-800 shadow-xl border-0">
        <div class="card-header border-b border-gray-100 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <svg class="w-6 h-6 mr-3 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                系统状态
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="flex items-center p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-xl">
                    <div class="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/40 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-bold text-gray-900 dark:text-white">数据库连接</div>
                        <div class="text-emerald-600 dark:text-emerald-400 font-semibold">正常运行</div>
                    </div>
                </div>

                <div class="flex items-center p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-xl">
                    <div class="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/40 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-bold text-gray-900 dark:text-white">文件存储</div>
                        <div class="text-emerald-600 dark:text-emerald-400 font-semibold">正常运行</div>
                    </div>
                </div>

                <div class="flex items-center p-4 bg-amber-50 dark:bg-amber-900/20 rounded-xl">
                    <div class="w-12 h-12 bg-amber-100 dark:bg-amber-900/40 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="font-bold text-gray-900 dark:text-white">邮件服务</div>
                        <div class="text-amber-600 dark:text-amber-400 font-semibold">待配置</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化图表
    initializeCharts();

    // 自动刷新统计数据（每5分钟）
    setInterval(function() {
        updateDashboardData();
    }, 5 * 60 * 1000);

    // 添加动画效果
    animateStatCards();
});

// 初始化图表
function initializeCharts() {
    // 报告趋势图表
    const reportsCtx = document.getElementById('reportsChart');
    if (reportsCtx) {
        new Chart(reportsCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '新增报告',
                    data: [2, 1, 3, 2, 4, 1, 2],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 请求状态饼图
    const requestsCtx = document.getElementById('requestsChart');
    if (requestsCtx) {
        new Chart(requestsCtx, {
            type: 'doughnut',
            data: {
                labels: ['待处理', '处理中', '已完成', '已拒绝'],
                datasets: [{
                    data: [{{ stats.pending_requests or 0 }}, 2, 8, 1],
                    backgroundColor: [
                        'rgb(251, 191, 36)',
                        'rgb(59, 130, 246)',
                        'rgb(34, 197, 94)',
                        'rgb(239, 68, 68)'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}

// 统计卡片动画
function animateStatCards() {
    const cards = document.querySelectorAll('.grid > div');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 更新仪表板数据
function updateDashboardData() {
    fetch('{{ url_for("admin.dashboard") }}?ajax=1')
        .then(response => response.json())
        .then(data => {
            // 更新统计数字
            updateStatNumbers(data);
            showNotification('数据已更新', 'success');
        })
        .catch(error => {
            console.error('更新数据失败:', error);
        });
}

// 更新统计数字
function updateStatNumbers(data) {
    const stats = document.querySelectorAll('.text-3xl');
    stats.forEach(stat => {
        const currentValue = parseInt(stat.textContent);
        const newValue = data[stat.dataset.stat] || currentValue;

        if (currentValue !== newValue) {
            animateNumber(stat, currentValue, newValue);
        }
    });
}

// 数字动画
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.round(start + (end - start) * progress);
        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="text-sm font-medium">${message}</span>
            <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-20px)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
</script>
{% endblock %}
