{% extends "base.html" %}

{% block title %}项目研究报告列表 - 项目研究报告平台{% endblock %}
{% block description %}浏览最新的项目研究报告，获取深度技术分析和项目评估{% endblock %}

{% block content %}
<!-- 🚀 现代化英雄区域 -->
<div class="hero-section relative min-h-screen flex items-center justify-center overflow-hidden">
    <!-- 动态渐变背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600"></div>
    <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

    <!-- 动态装饰元素 -->
    <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-float"></div>
    <div class="absolute top-40 right-32 w-24 h-24 bg-white/5 rounded-full blur-xl animate-float" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-32 left-1/3 w-20 h-20 bg-white/15 rounded-full blur-lg animate-float" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 right-20 w-16 h-16 bg-white/8 rounded-full blur-md animate-float" style="animation-delay: 0.5s;"></div>

    <div class="relative z-10 px-6 py-20 w-full">
        <div class="max-w-7xl mx-auto text-center">
            <!-- 🎯 主标题区域 -->
            <div class="mb-12 animate-fade-in">
                <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-full text-white/90 text-sm font-medium mb-6 animate-slide-down">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    AI驱动的智能分析平台
                </div>

                <h1 class="text-6xl md:text-8xl font-black mb-8 text-white text-shadow-lg leading-tight">
                    项目研究
                    <span class="block bg-gradient-to-r from-yellow-300 via-pink-300 to-purple-300 bg-clip-text text-transparent">
                        智能分析
                    </span>
                </h1>

                <p class="text-xl md:text-2xl mb-10 text-white/90 max-w-4xl mx-auto leading-relaxed font-light">
                    基于先进AI技术的深度项目分析平台，为您提供专业的技术评估、市场洞察和数据驱动的决策支持
                </p>
            </div>

            <!-- 🏷️ 特色功能标签 -->
            <div class="flex flex-wrap justify-center gap-4 mb-12 animate-slide-up">
                <div class="badge badge-lg bg-white/15 text-white border-white/20 backdrop-blur-md hover-lift">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    智能数据分析
                </div>
                <div class="badge badge-lg bg-white/15 text-white border-white/20 backdrop-blur-md hover-lift">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    专业技术评估
                </div>
                <div class="badge badge-lg bg-white/15 text-white border-white/20 backdrop-blur-md hover-lift">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    实时市场洞察
                </div>
                <div class="badge badge-lg bg-white/15 text-white border-white/20 backdrop-blur-md hover-lift">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                    决策支持系统
                </div>
            </div>

            <!-- 🎯 行动按钮组 -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-bounce-in">
                <button type="button" class="btn btn-xl btn-primary hover-lift ripple group" onclick="smoothScrollTo('#reports-section')">
                    <svg class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    探索研究报告
                    <div class="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-inherit"></div>
                </button>

                <button type="button" class="btn btn-xl btn-secondary hover-lift ripple group" data-modal-target="requestModal">
                    <svg class="w-6 h-6 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    申请项目分析
                    <div class="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-inherit"></div>
                </button>
            </div>

            <!-- 📊 快速统计 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16 max-w-4xl mx-auto animate-slide-up" style="animation-delay: 0.5s;">
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">{{ reports|length if reports else 0 }}+</div>
                    <div class="text-white/70 text-sm">研究报告</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">100+</div>
                    <div class="text-white/70 text-sm">项目分析</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">1000+</div>
                    <div class="text-white/70 text-sm">用户信赖</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-white mb-2">24/7</div>
                    <div class="text-white/70 text-sm">智能服务</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 🌊 现代化波浪装饰 -->
    <div class="absolute bottom-0 left-0 w-full overflow-hidden">
        <svg class="relative block w-full h-20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
            <defs>
                <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:rgba(249, 250, 251, 0.8);stop-opacity:1" />
                    <stop offset="50%" style="stop-color:rgba(249, 250, 251, 1);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgba(249, 250, 251, 0.8);stop-opacity:1" />
                </linearGradient>
            </defs>
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" fill="url(#waveGradient)"></path>
        </svg>
    </div>
</div>

<!-- 📊 现代化统计卡片区域 -->
<div id="stats-section" class="relative -mt-16 z-20 px-6">
    <div class="max-w-7xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- 研究报告统计 -->
            <div class="stats-card group animate-slide-up hover-lift">
                <div class="flex items-center justify-between p-8">
                    <div class="flex-1">
                        <div class="stats-value text-4xl font-black mb-2">{{ reports|length if reports else 0 }}+</div>
                        <div class="stats-label text-lg font-semibold mb-3">研究报告</div>
                        <div class="stats-trend">
                            <span class="trend-up">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +12%
                            </span>
                            <span class="text-gray-500 ml-1">较上月增长</span>
                        </div>
                    </div>
                    <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-10 h-10 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 项目分析统计 -->
            <div class="stats-card group animate-slide-up hover-lift" style="animation-delay: 0.1s;">
                <div class="flex items-center justify-between p-8">
                    <div class="flex-1">
                        <div class="stats-value text-4xl font-black mb-2">100+</div>
                        <div class="stats-label text-lg font-semibold mb-3">项目分析</div>
                        <div class="stats-trend">
                            <span class="trend-up">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +25%
                            </span>
                            <span class="text-gray-500 ml-1">本周新增</span>
                        </div>
                    </div>
                    <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-10 h-10 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 用户访问统计 -->
            <div class="stats-card group animate-slide-up hover-lift" style="animation-delay: 0.2s;">
                <div class="flex items-center justify-between p-8">
                    <div class="flex-1">
                        <div class="stats-value text-4xl font-black mb-2">1000+</div>
                        <div class="stats-label text-lg font-semibold mb-3">用户信赖</div>
                        <div class="stats-trend">
                            <span class="trend-up">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                +8%
                            </span>
                            <span class="text-gray-500 ml-1">日活跃用户</span>
                        </div>
                    </div>
                    <div class="stats-icon group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-10 h-10 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 🔍 现代化搜索和发现区域 -->
<div id="search-section" class="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900/20">
    <div class="max-w-7xl mx-auto px-6">
        <div class="card card-glass backdrop-blur-xl border-white/20 shadow-2xl animate-slide-up" style="animation-delay: 0.3s;">
            <div class="card-body p-12">
                <form method="GET" action="{{ url_for('public.index') }}" class="space-y-8">
                    <!-- 🎯 搜索标题区域 -->
                    <div class="text-center mb-10">
                        <div class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium mb-4">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            智能搜索引擎
                        </div>
                        <h2 class="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-4">
                            发现优质项目
                        </h2>
                        <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
                            使用我们的智能搜索引擎，快速找到您感兴趣的技术项目和深度分析报告
                        </p>
                    </div>

                    <!-- 🔍 高级搜索输入区域 -->
                    <div class="search-input max-w-4xl mx-auto">
                        <label for="search-input" class="sr-only">搜索项目</label>
                        <div class="relative group">
                            <svg class="search-icon group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <input type="text" id="search-input" name="search"
                                   class="form-input text-xl py-6 pl-16 pr-6 group-focus-within:ring-4 group-focus-within:ring-blue-500/20"
                                   placeholder="搜索项目名称、技术栈、关键词..."
                                   value="{{ search_query or '' }}"
                                   autocomplete="off">

                            <!-- 搜索建议下拉框 -->
                            <div class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 hidden" id="search-suggestions">
                                <div class="p-4">
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">热门搜索</div>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30">React</span>
                                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30">Vue.js</span>
                                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30">Python</span>
                                        <span class="px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30">AI/ML</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 🎯 搜索操作按钮组 -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <button type="submit" class="btn btn-lg btn-primary hover-lift ripple group">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            智能搜索
                        </button>

                        {% if search_query %}
                        <a href="{{ url_for('public.index') }}" class="btn btn-lg btn-outline hover-lift">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            清除搜索
                        </a>
                        {% endif %}

                        <button type="button" class="btn btn-lg btn-ghost hover-lift" data-modal-target="requestModal">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            申请新项目
                        </button>
                    </div>

                    <!-- 📊 搜索结果提示 -->
                    {% if search_query %}
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-2xl p-6 max-w-3xl mx-auto animate-slide-down">
                        <div class="flex items-center justify-center text-blue-700 dark:text-blue-300">
                            <svg class="w-6 h-6 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-lg font-semibold">
                                搜索 "<span class="font-black text-blue-800 dark:text-blue-200">{{ search_query }}</span>" 的结果
                            </span>
                        </div>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
</div>



<!-- 📋 现代化报告展示区域 -->
<div id="reports-section" class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-6">
        <!-- 🎯 区域标题 -->
        <div class="text-center mb-16">
            <div class="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-700 dark:text-blue-300 text-sm font-medium mb-6">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                {% if search_query %}智能搜索结果{% else %}精选项目报告{% endif %}
            </div>

            <h2 class="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-6">
                {% if search_query %}
                    搜索结果
                {% else %}
                    最新研究报告
                {% endif %}
            </h2>

            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                {% if search_query %}
                    为您找到 <span class="font-bold text-blue-600 dark:text-blue-400">{{ reports|length }}</span> 个相关项目
                {% else %}
                    探索最新的技术项目和深度分析，获取专业的技术洞察
                {% endif %}
            </p>
        </div>

        {% if reports %}

        <!-- 🎨 现代化项目卡片网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            {% for report in reports %}
            <article class="project-card group animate-slide-up hover-lift" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
                <!-- 🎨 项目头部区域 -->
                <div class="project-icon relative overflow-hidden">
                    <!-- 状态徽章 -->
                    <div class="status-badge">
                        <span class="badge badge-success badge-soft badge-pulse">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            已发布
                        </span>
                    </div>

                    <!-- 项目图标 -->
                    <div class="flex items-center justify-center h-full relative z-10">
                        <div class="w-24 h-24 bg-white/20 backdrop-blur-md rounded-3xl flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl">
                            <svg class="w-14 h-14 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- 装饰性元素 -->
                    <div class="absolute top-4 left-4 w-8 h-8 bg-white/10 rounded-full blur-sm"></div>
                    <div class="absolute bottom-6 right-6 w-6 h-6 bg-white/5 rounded-full blur-md"></div>
                </div>

                <!-- 📝 项目内容区域 -->
                <div class="p-8">
                    <!-- 项目标题和描述 -->
                    <div class="mb-6">
                        <h3 class="text-2xl font-black text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 line-clamp-2">
                            {{ report.project_name }}
                        </h3>
                        {% if report.project_description %}
                        <p class="text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed text-base">
                            {{ report.project_description }}
                        </p>
                        {% else %}
                        <p class="text-gray-500 dark:text-gray-500 italic">暂无项目描述</p>
                        {% endif %}
                    </div>

                    <!-- 📊 项目元信息 -->
                    <div class="space-y-4 mb-8">
                        <!-- 创建者 -->
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 shadow-lg">
                                {{ report.creator_name[0].upper() if report.creator_name else 'U' }}
                            </div>
                            <div>
                                <div class="font-semibold text-gray-700 dark:text-gray-300">{{ report.creator_name or '未知用户' }}</div>
                                <div class="text-xs text-gray-500">项目创建者</div>
                            </div>
                        </div>

                        <!-- 创建时间 -->
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mr-3 shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold text-gray-700 dark:text-gray-300">{{ report.created_at[:10] if report.created_at else '未知时间' }}</div>
                                <div class="text-xs text-gray-500">发布时间</div>
                            </div>
                        </div>

                        <!-- 官方网站 -->
                        {% if report.official_website %}
                        <div class="flex items-center text-sm">
                            <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mr-3 shadow-lg">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <a href="{{ report.official_website }}" target="_blank"
                                   class="font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200 truncate block">
                                    {{ report.official_website.replace('https://', '').replace('http://', '') }}
                                </a>
                                <div class="text-xs text-gray-500">官方网站</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 🎯 操作按钮组 -->
                    <div class="flex gap-3">
                        <a href="{{ url_for('public.view_report', report_id=report.id) }}"
                           class="btn btn-primary flex-1 hover-lift ripple group">
                            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            查看详情
                        </a>
                        {% if report.analysis_file %}
                        <a href="{{ url_for('public.view_analysis', report_id=report.id) }}"
                           class="btn btn-outline hover-lift group" title="查看分析">
                            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </article>
        </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- 🔍 空状态设计 -->
        <div class="text-center py-20">
            <div class="max-w-md mx-auto">
                <div class="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    {% if search_query %}
                        未找到相关项目
                    {% else %}
                        暂无研究报告
                    {% endif %}
                </h3>
                <p class="text-gray-600 dark:text-gray-400 mb-8">
                    {% if search_query %}
                        尝试使用不同的关键词搜索，或者申请新的项目分析
                    {% else %}
                        目前还没有发布的研究报告，请稍后再来查看
                    {% endif %}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    {% if search_query %}
                    <a href="{{ url_for('public.index') }}" class="btn btn-outline hover-lift">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        清除搜索
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-primary hover-lift" data-modal-target="requestModal">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        申请新项目分析
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if pagination and pagination.total_pages > 1 %}
<nav aria-label="报告分页" class="mt-8">
    <div class="flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if pagination.has_prev %}
            <a href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}"
               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
            </a>
            {% endif %}
            {% if pagination.has_next %}
            <a href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}"
               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
            </a>
            {% endif %}
        </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    显示第 <span class="font-medium">{{ (pagination.page - 1) * 10 + 1 }}</span> 到
                    <span class="font-medium">{{ pagination.page * 10 if pagination.page * 10 < pagination.total else pagination.total }}</span> 项，
                    共 <span class="font-medium">{{ pagination.total }}</span> 项结果
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if pagination.has_prev %}
                    <a href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}"
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}

                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.page %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ page_num }}
                        </span>
                        {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                        <a href="{{ url_for('public.index', page=page_num, search=search_query) }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ page_num }}
                        </a>
                        {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <a href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}"
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
</nav>
{% endif %}



<!-- Request Project Modal -->
<div id="requestModal" class="modal-container hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <!-- 背景遮罩 -->
    <div class="modal-backdrop" aria-hidden="true"></div>

    <!-- 模态框面板 -->
    <div class="modal-panel animate-scale-in max-w-2xl">
        <!-- 模态框头部 -->
        <div class="modal-header">
            <div class="flex items-center">
                <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="modal-title" id="modal-title">申请项目研究分析</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        提交您感兴趣的项目，我们将为您生成专业的分析报告
                    </p>
                </div>
            </div>
        </div>

        <!-- 模态框内容 -->
        <div class="modal-body">
            <!-- 信息提示 -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-5 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-300 mb-2">
                            申请须知
                        </h4>
                        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                            <li>• 我们使用AI技术对项目进行深度分析</li>
                            <li>• 报告生成通常需要1-3个工作日</li>
                            <li>• 完成后将通过邮件通知您</li>
                            <li>• 请确保提供的项目信息准确有效</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 申请表单 -->
            <form id="requestForm" class="space-y-6">
                <!-- 邮箱地址 -->
                <div class="form-group">
                    <label for="userEmail" class="form-label required">邮箱地址</label>
                    <div class="form-input-icon">
                        <input type="email" id="userEmail" name="email" class="form-input" required
                               placeholder="请输入您的邮箱地址">
                        <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                        </svg>
                    </div>
                    <p class="form-help">我们将通过此邮箱通知您报告完成情况</p>
                </div>

                <!-- 项目名称 -->
                <div class="form-group">
                    <label for="projectName" class="form-label required">项目名称</label>
                    <div class="form-input-icon">
                        <input type="text" id="projectName" name="project_name" class="form-input" required
                               placeholder="例如：React.js、Vue.js、Angular">
                        <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>

                <!-- 官方网站 -->
                <div class="form-group">
                    <label for="officialWebsite" class="form-label required">官方网站</label>
                    <div class="form-input-icon">
                        <input type="url" id="officialWebsite" name="official_website" class="form-input" required
                               placeholder="https://example.com">
                        <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                    </div>
                    <p class="form-help">请提供项目的官方网站或GitHub地址</p>
                </div>
            </form>
        </div>

        <!-- 模态框底部 -->
        <div class="modal-footer">
            <div class="flex flex-col sm:flex-row gap-3 sm:justify-end">
                <button type="button" data-modal-close class="btn btn-secondary order-2 sm:order-1">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    取消
                </button>
                <button type="button" id="submitRequest" class="btn btn-primary order-1 sm:order-2 hover-lift">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    提交申请
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 模态框功能
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // 添加动画
        setTimeout(() => {
            modal.querySelector('.modal-backdrop').classList.add('opacity-75');
            modal.querySelector('[role="dialog"]').classList.add('opacity-100', 'translate-y-0');
        }, 10);
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.querySelector('.modal-backdrop').classList.remove('opacity-75');
        modal.querySelector('[role="dialog"]').classList.remove('opacity-100', 'translate-y-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }, 300);
    }
}

// 项目申请表单处理
document.getElementById('submitRequest').addEventListener('click', function() {
    const form = document.getElementById('requestForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 显示加载状态
    const submitBtn = this;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading-spinner mr-2"></div>提交中...';
    submitBtn.disabled = true;

    // 基本验证
    if (!data.email || !data.project_name || !data.official_website) {
        showNotification('请填写所有必填字段', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        showNotification('请输入有效的邮箱地址', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // URL格式验证
    const urlRegex = /^https?:\/\/.+/;
    if (!urlRegex.test(data.official_website)) {
        showNotification('请输入有效的网站地址（以http://或https://开头）', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 提交请求
    fetch('{{ url_for("public.request_project") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message || '申请提交成功！我们会尽快处理您的请求。', 'success');
            hideModal('requestModal');
            form.reset();
        } else {
            showNotification('错误：' + (result.errors ? result.errors.join(', ') : '提交失败'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('提交时出现错误，请稍后重试', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} animate-slide-down`;

    const iconMap = {
        success: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        error: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        warning: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        info: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    };

    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${iconMap[type] || iconMap.info}
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-white hover:text-gray-200 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    // 添加到页面
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-20 right-4 z-50 space-y-2';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// 搜索功能增强
const searchInput = document.getElementById('search-input');
if (searchInput) {
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // 这里可以实现实时搜索建议
                console.log('Searching for:', query);
            }, 300);
        }
    });
}
</script>

<!-- 🎯 现代化项目申请模态框 -->
<div id="requestModal" class="modal-container fixed inset-0 z-50 overflow-y-auto hidden">
    <div class="modal-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 opacity-0"></div>

    <div class="flex min-h-full items-center justify-center p-4">
        <div class="modal-panel relative bg-white dark:bg-gray-800 mx-auto transition-all duration-300 opacity-0 translate-y-4 w-full max-w-2xl">
            <!-- 模态框头部 -->
            <div class="modal-header border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="modal-title text-2xl font-black text-gray-900 dark:text-white">申请新项目分析</h3>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">提交您希望我们分析的项目信息</p>
                    </div>
                    <button type="button" class="btn btn-ghost btn-sm" data-modal-close>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 模态框内容 -->
            <div class="modal-body">
                <form id="requestForm" method="POST" action="{{ url_for('public.request_project') }}" class="space-y-6">
                    {{ csrf_token() }}

                    <!-- 项目名称 -->
                    <div class="form-group">
                        <label for="project_name" class="form-label required">项目名称</label>
                        <div class="form-input-icon">
                            <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <input type="text" id="project_name" name="project_name" class="form-input"
                                   placeholder="请输入项目名称" required>
                        </div>
                        <div class="form-help">请提供准确的项目名称，便于我们进行分析</div>
                    </div>

                    <!-- 项目描述 -->
                    <div class="form-group">
                        <label for="project_description" class="form-label">项目描述</label>
                        <textarea id="project_description" name="project_description" rows="4" class="form-input"
                                  placeholder="请简要描述项目的功能、特点或您关注的方面..."></textarea>
                        <div class="form-help">详细的描述有助于我们提供更准确的分析</div>
                    </div>

                    <!-- 官方网站 -->
                    <div class="form-group">
                        <label for="official_website" class="form-label">官方网站</label>
                        <div class="form-input-icon">
                            <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                            </svg>
                            <input type="url" id="official_website" name="official_website" class="form-input"
                                   placeholder="https://example.com">
                        </div>
                        <div class="form-help">项目的官方网站或GitHub地址</div>
                    </div>

                    <!-- 联系邮箱 -->
                    <div class="form-group">
                        <label for="contact_email" class="form-label required">联系邮箱</label>
                        <div class="form-input-icon">
                            <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <input type="email" id="contact_email" name="contact_email" class="form-input"
                                   placeholder="<EMAIL>" required>
                        </div>
                        <div class="form-help">我们将通过此邮箱与您联系分析结果</div>
                    </div>
                </form>
            </div>

            <!-- 模态框底部 -->
            <div class="modal-footer">
                <div class="flex flex-col sm:flex-row gap-3 justify-end">
                    <button type="button" class="btn btn-outline" data-modal-close>
                        取消
                    </button>
                    <button type="submit" form="requestForm" class="btn btn-primary" data-loading-text="提交中...">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        提交申请
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
